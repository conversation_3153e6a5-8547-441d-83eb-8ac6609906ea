{{- if .Values.visionLLMConsumer.kedaEnabled }}
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ include "ai-engine.fullname" $ }}-keda-vision-llm-consumer
spec:
  scaleTargetRef:
    name: {{ include "ai-engine.fullname" $ }}-vision-llm-consumer
  minReplicaCount: {{ .Values.visionLLMConsumer.replicaCount }}
  maxReplicaCount: {{ .Values.visionLLMConsumer.maxReplicas }}
  cooldownPeriod: 300
  pollingInterval: 30
  fallback:
    failureThreshold: 3
    replicas: {{ .Values.visionLLMConsumer.replicaCount }}
  triggers:
    - type: aws-sqs-queue
      metadata:
        queueURL: {{ .Values.visionLLMConsumer.queueUrl }}
        queueLength: "{{ .Values.visionLLMConsumer.scaleupQueueLength }}"
        awsRegion: {{ .Values.visionLLMConsumer.awsRegion }}
      authenticationRef:
        name: keda-ai-engine-aws-auth
---
{{- end }}
{{- if .Values.visionLLMAGConsumer.kedaEnabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ include "ai-engine.fullname" $ }}-keda-vision-llm-ag-consumer
spec:
  scaleTargetRef:
    name: {{ include "ai-engine.fullname" $ }}-vision-llm-ag-consumer
  minReplicaCount: {{ .Values.visionLLMAGConsumer.replicaCount }}
  maxReplicaCount: {{ .Values.visionLLMAGConsumer.maxReplicas }}
  cooldownPeriod: 300
  pollingInterval: 30
  fallback:
    failureThreshold: 3
    replicas: {{ .Values.visionLLMAGConsumer.replicaCount }}
  triggers:
    - type: aws-sqs-queue
      metadata:
        queueURL: {{ .Values.visionLLMAGConsumer.queueUrl }}
        queueLength: "{{ .Values.visionLLMAGConsumer.scaleupQueueLength }}"
        awsRegion: {{ .Values.visionLLMAGConsumer.awsRegion }}
      authenticationRef:
        name: keda-ai-engine-aws-auth
---
{{- end }}
{{- if .Values.visionEventConsumer.kedaEnabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ include "ai-engine.fullname" $ }}-keda--vision-event-consumer
spec:
  scaleTargetRef:
    name: {{ include "ai-engine.fullname" $ }}-vision-event-consumer
  minReplicaCount: {{ .Values.visionEventConsumer.replicaCount }}
  maxReplicaCount: {{ .Values.visionEventConsumer.maxReplicas }}
  cooldownPeriod: 300
  pollingInterval: 30
  fallback:
    failureThreshold: 3
    replicas: {{ .Values.visionEventConsumer.replicaCount }}
  triggers:
    - type: aws-sqs-queue
      metadata:
        queueURL: {{ .Values.visionEventConsumer.queueUrl }}
        queueLength: "{{ .Values.visionEventConsumer.scaleupQueueLength }}"
        awsRegion: {{ .Values.visionEventConsumer.awsRegion }}
      authenticationRef:
        name: keda-ai-engine-aws-auth
---
{{- end }}
