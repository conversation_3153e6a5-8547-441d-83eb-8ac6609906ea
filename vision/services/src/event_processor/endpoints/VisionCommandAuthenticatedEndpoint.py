from datetime import datetime, timezone
from http import H<PERSON><PERSON>tatus
from typing import Any

import redis
import structlog
from flask import Blueprint, jsonify

from common_utils_v1.guard_endpoint import guard_endpoint
from db_controller import DBControllerV1
from models_rds.alarm_group import AlarmGroup
from models_rds.users import Users
from vision.services.src.event_processor.models.events import Resolution

logger = structlog.get_logger(
    "hakimo", module="vision_http_server_vision_command_authenticated_endpoint"
)


class VisionCommandAuthenticatedEndpoint:
    def __init__(self, controller: DBControllerV1, redis_client: redis.Redis):
        self.api = Blueprint(
            "vision_http_server_vision_query_authenticated_endpoint", __name__
        )
        self._ctrl_map = controller
        self._redis_client = redis_client

    @guard_endpoint(["location_alarm/detail:view"])
    def update_alarm_group_state(
        self, alarm_group_id: str, payload: dict, user: Users
    ):
        """
        Update the state of an alarm group
        """
        try:

            data = _unmarshal_update_alarm_group_state_payload(payload)
            operator_id = user.email

            alarm_group = self._ctrl_map.alarm_group.get_alarm_group_by_id(
                alarm_group_id
            )
            if not alarm_group:
                return self._create_response(
                    None, HTTPStatus.NOT_FOUND.value, "Alarm group not found"
                )

            _validate_current_alarm_group_state(
                alarm_group, data["resolution"]
            )

            if data["resolution"] == Resolution.SAFE:
                self._ctrl_map.alarm_group.update_alarm_group_safe_event(
                    alarm_group_id=alarm_group_id,
                    resolution=data["resolution"],
                    resolution_comment=data["resolution_comment"],
                    end_time=datetime.now(timezone.utc),
                    operator_id=operator_id,
                )
            elif data["resolution"] == Resolution.ESCALATION_CLOSE:
                self._ctrl_map.alarm_group.update_alarm_group_escalation_close_event(
                    alarm_group_id=alarm_group_id,
                    end_time=datetime.now(timezone.utc),
                    operator_id=operator_id,
                    resolution_comment=data["resolution_comment"],
                )

            return self._create_response(
                None,
                HTTPStatus.OK.value,
                "Alarm group state updated successfully",
            )

        except Exception as e:
            if isinstance(e, ValueError):
                return self._create_response(
                    None,
                    HTTPStatus.BAD_REQUEST.value,
                    str(e),
                )
            logger.error(
                "error_updating_alarm_group_state",
                error=str(e),
                payload=payload,
                exc_info=True,
            )
            raise

    def _create_response(self, payload: Any, status: int, message: str):
        """
        Centralized method to create JSON responses.

        Args:
            payload: The response payload
            status: HTTP status code
            message: Response message

        Returns:
            Flask response tuple (jsonify_response, status_code)
        """
        return (
            jsonify(
                {
                    "payload": payload,
                    "status": status,
                    "message": message,
                }
            ),
            status,
        )


def _unmarshal_update_alarm_group_state_payload(payload: dict) -> dict:
    """
    Unmarshal the payload for the update_alarm_group_state endpoint
    """
    resolution = payload.get("resolution")
    resolution_comment = payload.get("resolutionComment")

    if resolution not in [
        Resolution.SAFE.value,
        Resolution.ESCALATION_CLOSE.value,
    ]:
        raise ValueError(
            "Invalid resolution. It should be either safe or escalation_close"
        )

    return {
        "resolution": resolution,
        "resolution_comment": resolution_comment,
    }


def _validate_current_alarm_group_state(
    alarm_group: AlarmGroup, new_resolution: str
):
    """
    Validate the alarm group state
    """
    if (
        alarm_group.resolution == Resolution.ESCALATION_OPEN
        and new_resolution != Resolution.ESCALATION_CLOSE.value
    ):
        raise ValueError(
            "Cannot update alarm group state to safe from escalation open"
        )

    if (
        alarm_group.resolution == Resolution.OPEN
        and new_resolution != Resolution.SAFE.value
    ):
        raise ValueError(
            "Cannot update alarm group state to escalation close from open"
        )

    if alarm_group.resolution in [
        Resolution.ESCALATION_CLOSE,
        Resolution.SAFE,
    ]:
        raise ValueError("Alarm group is already resolved")
