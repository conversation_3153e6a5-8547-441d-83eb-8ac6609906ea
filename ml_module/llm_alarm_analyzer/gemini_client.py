import base64
import json
import mimetypes
import os
import re
import time
import typing

import requests
import structlog
from requests.exceptions import ReadTimeout, RequestException, Timeout

from common_utils.metrics_definitions import (
    RG_LLM_API_REQUEST_DURATION_SECONDS,
    RG_LLM_API_RESPONSES,
    RG_LLM_API_RETRIES,
    RG_LLM_API_RETRY_COUNTS,
)
from ml_module.llm_alarm_analyzer.base import (
    LLMClient,
    LLMRequestResponse,
    calculate_retry_sleep_time,
)

log = structlog.get_logger("hakimo", module="Gemini Client")


class GeminiClient(LLMClient):
    def __init__(
        self,
        api_key: str,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 3.0,
        jitter_factor: float = 1.0,
        timeout: int = 15,
    ):
        super().__init__(
            api_key=api_key,
            base_url="https://generativelanguage.googleapis.com",
            max_retries=max_retries,
            base_delay=base_delay,
            max_delay=max_delay,
            jitter_factor=jitter_factor,
            timeout=timeout,
        )

    def send_request(
        self,
        tenant_id: typing.Optional[str],
        alarm_phase: typing.Optional[str],
        request_text: typing.Optional[str],
        video_uri: typing.Optional[str] = None,
        video_bytes: typing.Optional[str] = None,
        images: typing.Optional[typing.List[str]] = None,
        image_mime_type: str = "image/webp",
        temperature: float = 0.1,
    ) -> LLMRequestResponse:
        request_parts = []
        if request_text:
            request_parts.append({"text": request_text})
        if video_uri:
            request_parts.append(
                {
                    "file_data": {
                        "mime_type": "video/mp4",
                        "file_uri": video_uri,
                    }
                }
            )
        if video_bytes:
            request_parts.append(
                {
                    "inline_data": {
                        "mime_type": "video/mp4",
                        "data": video_bytes,
                    }
                }
            )
        if images:
            for image_data in images:
                request_parts.append(
                    {
                        "inline_data": {
                            "mime_type": image_mime_type,
                            "data": image_data,
                        }
                    }
                )
        content_request = {
            "generationConfig": {"temperature": temperature},
            "contents": [
                {
                    "parts": request_parts,
                }
            ],
        }
        generate_headers = {"Content-Type": "application/json"}

        # List of HTTP status codes that should trigger a retry
        retryable_status_codes = [429, 500, 502, 503, 504]
        retry_count = 0
        response_data = None
        response_text = None
        generate_response = None
        request_start_time = time.time()

        # Initialize token count variables
        input_tokens = None
        output_tokens = None

        while True:
            # Initialize error variables for this iteration
            error_type = None
            error_details = None

            try:
                # Make the API request
                generate_response = requests.post(
                    f"{self.base_url}/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}",
                    headers=generate_headers,
                    json=content_request,
                    timeout=self.timeout,
                )
                response_text = generate_response.text
                # Parse the JSON response in the same try block to catch both network and JSON errors
                if generate_response.status_code == 200:
                    response_data = generate_response.json()
                    # Successfully received and parsed the response
                    break  # Exit the retry loop

                # HTTP error handling - retryable status codes
                if (
                    generate_response.status_code in retryable_status_codes
                    and retry_count < self.max_retries
                ):
                    status = generate_response.status_code
                    error_type = f"http_{status}"
                    error_details = f"status code {status}"
                    response_text = None  # No response text for HTTP errors
                else:
                    # Non-retryable HTTP error or max retries exceeded
                    log.error(
                        "Gemini API request failed",
                        status_code=generate_response.status_code,
                        response=generate_response.text,
                    )
                    return LLMRequestResponse(
                        success=False,
                        data={},
                        full_output=response_text,
                        error_reason="http_failure",
                    )

            except json.JSONDecodeError as json_err:
                # JSON parsing error
                if retry_count < self.max_retries:
                    error_type = "malformed JSON"
                    error_details = str(json_err)
                    # Add partial response text for debugging
                    if generate_response is not None and hasattr(
                        generate_response, "text"
                    ):
                        response_text = generate_response.text[:200]
                    else:
                        response_text = None
                else:
                    # Max retries exceeded
                    log.error(
                        "Failed to parse Gemini API response after max retries",
                        error=str(json_err),
                        max_retries=self.max_retries,
                    )
                    return LLMRequestResponse(
                        success=False,
                        data={},
                        full_output=response_text or "",
                        error_reason="json_decode_failure",
                    )

            except (RequestException, Timeout, ReadTimeout) as e:
                # Network/connection error
                if retry_count < self.max_retries:
                    if isinstance(e, Timeout):
                        error_type = "timeout"
                    elif isinstance(e, ReadTimeout):
                        error_type = "read_timeout"
                    else:
                        error_type = "connection_error"
                    error_details = str(e)
                    response_text = None
                else:
                    # Max retries exceeded
                    log.error(
                        "Failed to connect to Gemini API after max retries",
                        error=str(e),
                        max_retries=self.max_retries,
                    )
                    return LLMRequestResponse(
                        success=False,
                        data={},
                        full_output=response_text or "",
                        error_reason="connection_failure",
                    )

            # Common retry handling for all error types
            # Only proceed if we have error details (meaning we should retry)
            if error_type is None or error_details is None:
                # This shouldn't happen, but if it does, break to avoid infinite loop
                log.error("Unexpected state: no error details for retry")
                return LLMRequestResponse(
                    success=False,
                    data={},
                    full_output="",
                    error_reason="internal_retry_logic",
                )
            RG_LLM_API_RETRIES.labels(
                tenant=tenant_id,
                alarm_phase=alarm_phase,
                error_type=error_type,
            ).inc()

            retry_count += 1
            sleep_time = calculate_retry_sleep_time(
                retry_count=retry_count,
                base_delay=self.base_delay,
                max_delay=self.max_delay,
                jitter_factor=self.jitter_factor,
            )

            # Log the retry with the appropriate error type
            log_args = {
                "retry_count": retry_count,
                "max_retries": self.max_retries,
                "error": error_details,
                "delay_seconds": sleep_time,
            }

            # Only add response_text if it exists and is not None
            if response_text is not None:
                log_args["response_text"] = response_text

            log.warning(
                f"Gemini API {error_type}, retrying full request", **log_args
            )

            time.sleep(sleep_time)

        status_code = generate_response.status_code
        duration = time.time() - request_start_time

        try:
            RG_LLM_API_REQUEST_DURATION_SECONDS.labels(
                tenant=tenant_id,
                alarm_phase=alarm_phase,
            ).observe(duration)

            RG_LLM_API_RESPONSES.labels(
                tenant=tenant_id,
                alarm_phase=alarm_phase,
                status_code=str(status_code),
            ).inc()

            RG_LLM_API_RETRY_COUNTS.labels(
                tenant=tenant_id,
                alarm_phase=alarm_phase,
            ).observe(retry_count)

        except Exception as e:
            log.warning(
                "Failed to update Gemini API metrics",
                tenant_id=tenant_id,
                alarm_phase=alarm_phase,
                error=str(e),
            )
        # Extract token counts if available
        if response_data and "usageMetadata" in response_data:
            input_tokens = response_data.get("usageMetadata", {}).get(
                "promptTokenCount"
            )
            output_tokens = response_data.get("usageMetadata", {}).get(
                "candidatesTokenCount"
            )

        out = [
            part["text"]
            for candidate in response_data.get("candidates", [])
            for part in candidate.get("content", {}).get("parts", [])
        ]
        full_out = "\n".join(out)

        # Strip leading/trailing spaces and remove markdown markers
        cleaned_output = re.sub(r"```json\n?|```", "", full_out).strip()
        try:
            out_dict = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            log.error(
                "JSON decoding of Gemini output failed",
                json_error=str(e),
                cleaned_output=cleaned_output,
            )
            return LLMRequestResponse(
                success=False,
                data={},
                full_output=full_out,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                error_reason="response_parse_error",
            )

        return LLMRequestResponse(
            success=True,
            data=out_dict,
            full_output=full_out,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
        )

    def upload_video(
        self, video_path: str
    ) -> typing.Tuple[bool, typing.Optional[str]]:
        mime_type = (
            mimetypes.guess_type(video_path)[0] or "application/octet-stream"
        )
        num_bytes = os.path.getsize(video_path)
        display_name = os.path.basename(video_path)

        # Step 1: Start resumable upload request
        data = {"file": {"display_name": display_name}}
        headers = {
            "X-Goog-Upload-Protocol": "resumable",
            "X-Goog-Upload-Command": "start",
            "X-Goog-Upload-Header-Content-Length": str(num_bytes),
            "X-Goog-Upload-Header-Content-Type": mime_type,
            "Content-Type": "application/json",
        }
        response = requests.post(
            f"{self.base_url}/upload/v1beta/files?key={self.api_key}",
            headers=headers,
            json=data,
        )

        upload_url = response.headers.get("X-Goog-Upload-URL")
        if not upload_url:
            log.error("Failed to get upload URL")
            return False, None

        # Step 2: Upload the video
        with open(video_path, "rb") as video_file:
            upload_headers = {
                "Content-Length": str(num_bytes),
                "X-Goog-Upload-Offset": "0",
                "X-Goog-Upload-Command": "upload, finalize",
            }
            upload_response = requests.post(
                upload_url, headers=upload_headers, data=video_file
            )

        file_info = upload_response.json()
        file_uri = file_info.get("file", {}).get("uri")
        file_name = file_info.get("file", {}).get("name")
        state = file_info.get("file", {}).get("state")

        # Step 3: Ensure the state of the video is 'ACTIVE'
        retry_number = 0
        while state == "PROCESSING" and retry_number < 5:
            time.sleep(1)
            check_response = requests.get(
                f"{self.base_url}/v1beta/{file_name}?key={self.api_key}"
            )
            retry_number += 1
            state = check_response.json().get("state")
        if state != "ACTIVE":
            log.error(
                "Gemini video processing failed or is incomplete.",
                retry_number=retry_number,
                file_name=file_name,
            )
            return False, None
        return True, file_uri

    def send_request_with_video(
        self,
        tenant_id: typing.Optional[str],
        text: str,
        video_path: str,
        use_video_uri: bool = False,
        temperature: float = 0.1,
    ) -> LLMRequestResponse:
        file_uri, video_bytes = None, None
        if use_video_uri:
            upload_success, file_uri = self.upload_video(video_path)
            if not upload_success:
                return LLMRequestResponse(error_reason="upload_failed")
        else:
            with open(video_path, "rb") as video_file:
                video_bytes = base64.b64encode(video_file.read()).decode(
                    "utf-8"
                )
        llm_response: LLMRequestResponse = self.send_request(
            tenant_id=tenant_id,
            alarm_phase="raw",
            request_text=text,
            video_uri=file_uri,
            video_bytes=video_bytes,
            temperature=temperature,
        )
        return llm_response
