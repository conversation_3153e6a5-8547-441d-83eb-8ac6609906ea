import time
import typing
from datetime import datetime

import structlog

from common_utils.time_utils import format_utc_to_local_with_weekday
from ml_module.llm_alarm_analyzer.base import (
    AIResultLA,
    AIResultRA,
    LLMRequestResponse,
)
from ml_module.llm_alarm_analyzer.gemini_client import GeminiClient

log = structlog.get_logger("hakimo", module="LLM Alarm Analyzer")


class LLMAlarmAnalyzer:
    def __init__(
        self,
        api_key: str,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 3.0,
        jitter_factor: float = 1.0,
        timeout: int = 15,
    ):
        self._client = GeminiClient(
            api_key=api_key,
            max_retries=max_retries,
            base_delay=base_delay,
            max_delay=max_delay,
            jitter_factor=jitter_factor,
            timeout=timeout,
        )

    def analyze_location_alarm(
        self,
        tenant_id: typing.Optional[str],
        previous_analysis: typing.List[typing.Dict[str, str]],
        sop: typing.Optional[str],
        location_timezone: str,
        temperature: float = 0.1,
    ) -> AIResultLA:
        try:
            prompt_text = [
                "This is a site that is being monitored by a remote security operator. "
                f"The local timezone for the site is {location_timezone}. "
                "Following are the details of raw video events detected by the cameras on the site "
                "along with an explanation of what is happening in those videos:\n"
            ]
            for entry in previous_analysis:
                prompt_text.append(entry["text"] + "\n")

            prompt_text.append(
                f"This is the Standard Operating Procedure (SOP) the operator has to follow: \nSOP_START\n{sop}\nSOP_END\n"
                "Based on this information, is there any unauthorized or suspicious activity happening at the site? "
                "Do not assume that any person/vehicle is authorized unless you are absolutely certain. "
                "Provide an overall summary and a recommendation based on all the events on whether to Resolve or Escalate the activity. "
                "If any of the video events are suspicious, the activity should be escalated. "
                "The recommendation should be Resolve if there is nothing suspicious and no further action is needed. The score should be 40."
                "or Escalate if the operator should take further action. "
                "If the activity matches any of the activities mentioned under the emergency situations in the SOP, Escalate the activity with a score of 90. "
                "If the activity matches any of the activities mentioned under the non-emergency situations in the SOP, Escalate the activity with a score of 70. "
                "If the activity is suspicious and doesn't fall into any of the above categories, Escalate the activity with a score of 100. "
                "Respond in json format with keys 'explanation', 'summary', 'recommendation' and 'score' where"
                "explanation should be a detailed explanation of the activity, summary should be a short summary of the activity, "
                "recommendation should be either Resolve or Escalate based on the explanation and score should be a number between 0 and 100, as given in the cases above."
            )
            prompt_text = "".join(prompt_text)
            log.debug("Gemini Prompt", prompt_text=prompt_text)

            llm_response: LLMRequestResponse = self._client.send_request(
                tenant_id=tenant_id,
                alarm_phase="location",
                request_text=prompt_text,
                temperature=temperature,
            )
            if not llm_response.success:
                return AIResultLA(
                    reason="api_error",
                    input_tokens=llm_response.input_tokens,
                    output_tokens=llm_response.output_tokens,
                )
            loc_explanation = llm_response.data.get("explanation")
            loc_summary = llm_response.data.get("summary")
            loc_reco = llm_response.data.get("recommendation")
            loc_score = llm_response.data.get("score")
            if loc_score is not None:
                try:
                    loc_score = int(loc_score)
                except (ValueError, TypeError):
                    log.warning("loc_score is not an int", loc_score=loc_score)
                    loc_score = None
            log.info(
                "Gemini analysis of location alarm complete",
                full_output=llm_response.full_output,
                explanation=loc_explanation,
                summary=loc_summary,
                loc_reco=loc_reco,
                loc_score=loc_score,
            )
            return AIResultLA(
                success=True,
                reason=None,
                explanation=loc_explanation,
                summary=loc_summary,
                recommendation=loc_reco,
                score=loc_score,
                input_tokens=llm_response.input_tokens,
                output_tokens=llm_response.output_tokens,
            )

        except Exception:
            return AIResultLA(reason="analysis_error")

    def analyze_alarm(
        self,
        tenant_id: typing.Optional[str],
        video_path: str,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_timezone: str,
        send_inline_video: bool = False,
        send_inline_frames: bool = False,
        temperature: float = 0.1,
        frames: typing.Optional[typing.List[str]] = None,
    ) -> AIResultRA:
        vs_str = format_utc_to_local_with_weekday(
            video_times[0], location_timezone
        )
        ve_str = format_utc_to_local_with_weekday(
            video_times[1], location_timezone
        )
        start_time = time.time()

        # Prepare prompt text
        if send_inline_frames and frames is not None:
            input_description = (
                "These are image frames from the camera feed of a site monitored by a remote security operator. "
                "The frames are captured at 1 frame per second whenever motion is detected. "
                f"You are provided a sequence of frames from camera '{camera_name}' at the site. "
                f"The frame capture starts at {vs_str} and ends at {ve_str}. "
            )
        else:
            input_description = (
                f"You are provided a video clip from camera '{camera_name}' at a site monitored by a remote security operator. "
                f"The video starts at {vs_str} and ends at {ve_str}. "
            )

        prompt_text = (
            "You are an advanced AI security analyst. "
            + input_description
            + f"The local timezone of the site is {location_timezone}. "
            f"This is the Standard Operating Procedure (SOP) that the operator must follow, outlining activities that should be escalated to local security or law enforcement: \nSOP_START\n{sop}\nSOP_END\n"
            "Your task is to review the input carefully and determine if any suspicious or unauthorized activity is present, as defined by the SOP. "
            "If suspicious persons or moving vehicles are detected, describe their appearance, behavior, and any relevant context. "
            "Is there anything suspicious happening in the video that is mentioned in the Standard Operating Procedure (SOP)? "
            "Is there any unauthorized or suspicious activity happening in the video that is mentioned in the Standard Operating Procedure (SOP)? "
            "Do not assume that any person or vehicle is authorized unless there is clear evidence. "
            "Based on your assessment, provide a clear recommendation to the operator on whether to **Resolve** (if there is nothing suspicious and no further action needed) or **Escalate** (further investigation or action is required). "
            "Respond strictly in JSON format using double quotes for all keys and string values. The JSON must contain only the following two keys:\n"
            '- "explanation": A detailed explanation of your analysis.\n'
            '- "recommendation": One of "Resolve" or "Escalate".\n'
            "Example format:\n"
            '{\n  "explanation": "Some explanation here.",\n  "recommendation": "Resolve"\n}\n'
            "Do not include any extra text, markdown formatting, or code blocks. Your response must be a valid JSON object."
        )
        log.debug("Gemini Prompt", prompt_text=prompt_text)

        try:
            # Handle frames or video
            if send_inline_frames and frames is not None:
                llm_response: LLMRequestResponse = self._client.send_request(
                    tenant_id=tenant_id,
                    alarm_phase="raw",
                    request_text=prompt_text,
                    images=frames,
                    image_mime_type="image/webp",
                    temperature=temperature,
                )

            else:
                llm_response: LLMRequestResponse = (
                    self._client.send_request_with_video(
                        tenant_id=tenant_id,
                        text=prompt_text,
                        video_path=video_path,
                        use_video_uri=not send_inline_video,
                        temperature=temperature,
                    )
                )
            if not llm_response.success:
                return AIResultRA(
                    success=False,
                    reason=llm_response.error_reason,
                    analysis=None,
                    recommendation=None,
                    input_tokens=llm_response.input_tokens,
                    output_tokens=llm_response.output_tokens,
                )

            # Process analysis results
            analysis = llm_response.data.get("explanation")
            recommendation = llm_response.data.get("recommendation")

            log.info(
                "Gemini analysis complete",
                llm_analysis_time=time.time() - start_time,
                full_output=llm_response.full_output,
                analysis=analysis,
                recommendation=recommendation,
                input_tokens=llm_response.input_tokens,
                output_tokens=llm_response.output_tokens,
            )

            return AIResultRA(
                success=True,
                analysis=analysis,
                recommendation=recommendation,
                input_tokens=llm_response.input_tokens,
                output_tokens=llm_response.output_tokens,
            )

        except Exception:
            return AIResultRA(reason="analysis_error")
