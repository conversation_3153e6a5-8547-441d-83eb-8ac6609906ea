import tempfile
import typing

import structlog

import controller as ctrl
from controller.alarm.alarm_filters import AlarmFilters
from interfaces.tags import Tags
from ml_module.alarm_side_effect_processors.llm_alarm_analyzer.enterprise import (
    EnterpriseLLMAlarmAnalyzerSideEffect,
)
from ml_module.utils import download_alarm_video
from models_rds.rds_client import RDSClient

log = structlog.get_logger(
    "hakimo", module="Eval Enterprise LLM Alarm Analyzer"
)


def print_stats(pred_matches: typing.List[typing.Dict], n_entities: int = -1):
    tp = 0
    fp = 0
    tn = 0
    fn = 0
    select_count = 0
    for pred in pred_matches:
        if n_entities > 1:
            if pred["n_entities"] > n_entities:
                continue
        select_count += 1
        if pred["is_tg"]:
            if pred["tg_pred"]:
                tp += 1
            else:
                fn += 1
        else:
            if not pred["tg_pred"]:
                tn += 1
            else:
                fp += 1
    accuracy = (tp + tn) / (select_count + 1e-9)
    precision = tp / (tp + fp) if tp + fp > 0 else 0
    recall = tp / (tp + fn) if tp + fn > 0 else 0
    f1 = (
        2 * precision * recall / (precision + recall)
        if precision + recall > 0
        else 0
    )
    print("=" * 10)
    print(f"n Entities : {n_entities}")
    print(f"Accuracy: {accuracy}")
    print(f"Precision: {precision}")
    print(f"Recall: {recall}")
    print(f"F1: {f1}")
    print(f"Ignored alarms : {len(pred_matches) - select_count}")
    print(f"Total alarms : {len(pred_matches)}")
    print("=" * 10 + "\n")


def eval_enterprise():
    import csv
    import glob
    import json
    import os
    import sys

    from tqdm import tqdm

    db = RDSClient()
    cm = ctrl.ControllerMap(db.db_adapter)
    se = EnterpriseLLMAlarmAnalyzerSideEffect(cm)

    csv_path = sys.argv[1]
    alarms_dir = sys.argv[2]
    if len(sys.argv) > 3:
        experiment_name = sys.argv[3]
    else:
        experiment_name = "llm_alarm_analyzer"

    pred_matches = []
    with open(csv_path, "r") as f:
        reader = csv.DictReader(f)
        for row in tqdm(list(reader)):
            alarm_id = row["Alarm ID"]
            alm = cm.alarm.fetch_alarms(
                None,
                filters=AlarmFilters(
                    alarm_uuids=[alarm_id],
                    tenant=None,
                ),
                limit=None,
            )[0]
            is_tg = row["Is TG"] == "TRUE"
            assert alm is not None
            alarm_id_dir = os.path.join(alarms_dir, alarm_id)
            if not os.path.isdir(alarm_id_dir):
                continue
            tags_file = list(
                glob.glob(os.path.join(alarm_id_dir, "*tags.json"))
            )
            if not tags_file:
                continue
            with open(tags_file[0], "r") as f:
                tags_json = json.load(f)
            tags_json["tags"] = ["ENTRY", "UNAUTHORIZED_ENTRY"]
            tags = Tags.from_json(tags_json)
            alarm = db.get_alarm_object_from_raw_alarm(alm[0])
            if alarm is None or alarm.scene_info is None:
                log.info(
                    "Skipping alarm as alarm is None or scene_info is None",
                    alarm_id=alarm_id,
                )
                continue
            with tempfile.TemporaryDirectory() as tmp_dir:
                alarm_video_path = download_alarm_video(
                    alarm.video_path, tmp_dir
                )
                not_tg, response = se._llm_enterprise_classify(
                    alarm, alarm_video_path, tags
                )
            data = {
                "alarm_id": alarm_id,
                "is_tg": is_tg,
                "response": response,
                "tg_pred": not not_tg,
                "n_entities": len(tags.entities),
            }
            log.info("Alarm classified", **data)
            pred_matches.append(data)
    with open(f"predictions_{experiment_name}.json", "w") as f:
        json.dump(pred_matches, f, indent=4)
    # calculate accuracy, f1, precision, recall
    for i in range(3, 9):
        print_stats(pred_matches, i)


if __name__ == "__main__":
    eval_enterprise()
