import abc
import sys
import typing
from dataclasses import dataclass
from datetime import datetime
from multiprocessing import Process, Queue
from uuid import uuid4

import structlog

import controller as ctrl
from common_utils.db_pool import ctrl_pool
from common_utils.metrics_definitions import (
    RG_LLM_LOCATION_ALARM_PROCESSING_SECONDS,
    RG_LLM_LOCATION_ALARM_TOKENS,
    RG_LLM_LOCATION_ALARMS_PROCESSED,
    RG_LLM_RAW_ALARM_PROCESSING_SECONDS,
    RG_LLM_RAW_ALARM_TOKENS,
    RG_LLM_RAW_ALARMS_PROCESSED,
    RG_LLM_TASK_QUEUE_LENGTH,
    RG_LLM_WORKER,
)
from common_utils.typing_helpers import NUMBER
from config import backend_config as config
from interfaces.alarm import ALARM_TYPE_MOTION, Alarm, AlarmState
from interfaces.location_alarms import (
    LocationAlarmStatus,
)
from interfaces.tags import Tags
from interfaces.tenant_config import (
    AlarmProcessingConfig,
    LLMAlarmAnalyzerConfig,
)
from ml_module.alarm_side_effect_processors.base import BaseSideEffectHandler
from ml_module.alarm_side_effect_processors.llm_alarm_analyzer.utils import (
    AlarmEventHandler,
)
from ml_module.llm_alarm_analyzer.analyzer import (
    LLMAlarmAnalyzer,
    LLMRequestResponse,
)
from ml_module.ml_interfaces.service_response import MLServiceResponse
from models_rds.location_alarms.location_alarms import LocationAlarms
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo", module="LLM Alarm Analyzer Side Effect")


class LLMAlarmAnalyzerSideEffect(BaseSideEffectHandler):
    def __init__(
        self,
        controller: ctrl.ControllerMap,
        side_effect_order: typing.Optional[str] = None,
    ):
        self._analyzer = LLMAlarmAnalyzer(
            api_key=config.HAIE.GEMINI_ALARM_ANALYZER_KEY
        )
        self.side_effect_order = side_effect_order
        self._controller = controller
        self._rds_client = RDSClient(controller.db)
        self.task_queue: Queue = Queue(
            maxsize=config.HAIE.GEMINI_ALARM_ANALYZER_QUEUE_SIZE
        )
        self.task_runners: typing.List[Process] = []
        self._metrics: typing.Dict[str, typing.Any] = {
            "rg_llm_task_queue_length": RG_LLM_TASK_QUEUE_LENGTH,
            "rg_llm_worker": RG_LLM_WORKER,
            "rg_llm_raw_alarms_processed": RG_LLM_RAW_ALARMS_PROCESSED,
            "rg_llm_location_alarms_processed": RG_LLM_LOCATION_ALARMS_PROCESSED,
            "rg_llm_raw_alarm_processing_seconds": RG_LLM_RAW_ALARM_PROCESSING_SECONDS,
            "rg_llm_location_alarm_processing_seconds": RG_LLM_LOCATION_ALARM_PROCESSING_SECONDS,
            "rg_llm_raw_alarm_tokens": RG_LLM_RAW_ALARM_TOKENS,
            "rg_llm_location_alarm_tokens": RG_LLM_LOCATION_ALARM_TOKENS,
        }
        for i in range(config.HAIE.GEMINI_ALARM_ANALYZER_WORKERS):
            p = Process(target=self.take_from_queue)
            p.start()
            self.task_runners.append(p)

        RG_LLM_WORKER.set(config.HAIE.GEMINI_ALARM_ANALYZER_WORKERS)
        RG_LLM_TASK_QUEUE_LENGTH.set(self.task_queue.qsize())

    def take_from_queue(self):
        log.info("Starting Worker")

        while True:
            RG_LLM_TASK_QUEUE_LENGTH.set(self.task_queue.qsize())
            task = self.task_queue.get()
            if task is not None:
                log.info("[Worker] Processing task")
                self.process_alarm(
                    *task,
                )
            else:
                log.info("[Worker] Ending Gemini Worker")
                RG_LLM_WORKER.dec()
                break

    def shutdown(self):
        log.info("[Main] Shuting down task runner")
        for i in range(config.HAIE.GEMINI_ALARM_ANALYZER_WORKERS):
            self.task_queue.put(None)
        for i in range(config.HAIE.GEMINI_ALARM_ANALYZER_WORKERS):
            self.task_runners[i].join()
        RG_LLM_WORKER.set(0)
        RG_LLM_TASK_QUEUE_LENGTH.set(0)
        log.info("[Main] Worker shut down")

    @abc.abstractmethod
    def _should_process(
        self,
        alarm: Alarm,
        new_tap: NUMBER,
        processing_config: typing.Optional[AlarmProcessingConfig],
        new_tags: Tags,
    ) -> bool:
        raise NotImplementedError

    @abc.abstractmethod
    def _use_default_llm_alarm_analyzer_config(
        self,
    ) -> bool:
        raise NotImplementedError

    @abc.abstractmethod
    def _setup_alarm_data(
        self,
        alarm: Alarm,
        analyzeLocationAlarm: bool,
        usePreviousLocationAlarmExplanation: bool,
    ):
        raise NotImplementedError

    @abc.abstractmethod
    def _setup(self, alarm: Alarm):
        raise NotImplementedError

    @abc.abstractmethod
    def process_alarm(
        self,
        alarm: Alarm,
        tags: Tags,
        loc_alarm: typing.Optional[LocationAlarms],
        resolve_raw_alarms: bool,
        escalate_raw_alarms: bool,
        resolve_loc_alarms: bool,
        update_location_alarm_tap: bool,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_name: str,
        location_timezone: str,
        previous_analysis: typing.List[typing.Dict[str, str]],
        previous_alarms: typing.List[str],
        analyzeRawAlarm: bool,
        analyzeLocationAlarm: bool,
        send_inline_video: bool,
        send_inline_frames: bool,
        check_threats: bool,
        slow_down_video_flag: bool,
        temperature: float,
    ):
        raise NotImplementedError

    @abc.abstractmethod
    def _should_resolve_raw_alarm(
        self,
        resolveRawAlarm: bool,
        tenant_id: str,
        source_entity_id: str,
        n_entities: int,
    ) -> bool:
        raise NotImplementedError

    def _publish_threat_alarm(self, alarm: Alarm, description: str):
        if "fire" in description.lower():
            alarm_type = "Fire Detected"
            true_alarm_probability = 98
        else:
            alarm_type = "Weapon Detected"
            true_alarm_probability = 99
        alarm_info = {
            "alarm_type": alarm_type,
            "alarm_time": alarm.created_at_utc,
            "source_id": "derived/threat/" + (alarm.source_id or str(uuid4())),
            "source_system": alarm.source_system,
            "tenant_id": alarm.tenant_id,
            "video_path": alarm.video_path,
            "state": AlarmState.PROCESSED,
            "processing_start_time_utc": alarm.processing_start_time_utc,
            "processing_end_time_utc": alarm.processing_end_time_utc,
        }
        if alarm.source_entity_type == "DOOR":
            alarm_info["door_uuid"] = alarm.door_uuid or alarm.source_entity_id
        new_alarm = Alarm(**alarm_info)
        new_alarm.source_entity_type = alarm.source_entity_type
        assert alarm.source_entity_id is not None
        new_alarm.source_entity_id = alarm.source_entity_id
        new_alarm.display = True
        raw_alarm_uuid = self._rds_client.write_update_raw_alarms(new_alarm)
        self._rds_client.update_raw_alarms_with_video_metadata(
            raw_alarm_uuid,
            alarm.video_start_time_utc,
            alarm.video_end_time_utc,
            video_path=alarm.video_path,
        )
        threat_alarm = self._rds_client.get_alarm_object(
            raw_alarm_uuid, alarm.tenant_id
        )
        assert threat_alarm is not None
        with ctrl_pool.get() as controller:  # type: ignore
            controller.alarm_media.add_media(
                raw_alarm_uuid, alarm.video_path, media_type="video"
            )
            controller.alarm.add_comment(
                new_alarm.alarm_uuid,
                new_alarm.tenant_id,
                description,
            )
            controller.alarm.update_tap(
                new_alarm.alarm_uuid,
                new_alarm.tenant_id,
                true_alarm_probability,
            )
            controller.alarm.mark_alarm_pending(threat_alarm)

    def _llm_threat_classify(
        self,
        tenant_id: str,
        alarm_video_path: str,
        tags: Tags,
        do_crop: bool = False,
    ) -> typing.Tuple[bool, str]:
        text = (
            "You are a security agent watching for an threats in a video stream"
            "Look for any person in the video with any weapon such as a gun, knife or any lethal weapons, "
            "if so describe the appearance of the person and at what timestamp the weapon is visible in the summary. "
            "The recommendation should be Resolve if there is nothing suspicious and no further action is needed, "
            "or Escalate if the operator should take further action.\n"
            "Also watch for fire or smoke and Escalate in that scenario as well.\n"
            "Respond in json format with keys 'summary' and 'recommendation' where recommendation is either 'Resolve' or 'Escalate'. "
        )
        if do_crop:
            cropped_alarm_video_path = AlarmEventHandler.crop_video(
                alarm_video_path, tags
            )
        else:
            cropped_alarm_video_path = alarm_video_path

        llm_response: LLMRequestResponse = (
            self._analyzer._client.send_request_with_video(
                tenant_id=tenant_id,
                text=text,
                video_path=cropped_alarm_video_path,
                use_video_uri=False,
            )
        )
        log.info(
            "Received response from gemini",
            recommendation=llm_response.data.get("recommendation", "missing"),
            summary=llm_response.data.get("summary", "missing"),
        )
        if not llm_response.success:
            return True, ""
        resolve = True
        if "escalate" in llm_response.data["recommendation"].lower():
            resolve = False
        return resolve, llm_response.data["summary"]

    def handle_side_effect(
        self,
        processing_config: typing.Optional[AlarmProcessingConfig],
        alarm: Alarm,
        new_tap: NUMBER,  # pylint: disable=unused-argument
        new_tags: Tags,  # pylint: disable=unused-argument
        old_tap: typing.Optional[NUMBER],  # pylint: disable=unused-argument
        old_tags: typing.Optional[Tags],  # pylint: disable=unused-argument
        new_state: AlarmState,  # pylint: disable=unused-argument
        ml_service_response: typing.Optional[MLServiceResponse] = None,  # pylint: disable=unused-argument
    ):
        if not self._should_process(
            alarm, new_tap, processing_config, new_tags
        ):
            return
        (
            sop_text,
            source_camera_name,
            checkThreats,
            location_timezone,
            location_name,
        ) = self._setup(alarm)
        assert alarm.source_entity_id is not None
        if self._use_default_llm_alarm_analyzer_config():
            llm_alarm_analyzer_config = LLMAlarmAnalyzerConfig()
            if (
                processing_config is not None
                and processing_config.llmAlarmAnalyzerConfig is not None
            ):
                llm_alarm_analyzer_config = (
                    processing_config.llmAlarmAnalyzerConfig
                )
        else:
            assert processing_config is not None
            assert processing_config.llmAlarmAnalyzerConfig is not None
            llm_alarm_analyzer_config = (
                processing_config.llmAlarmAnalyzerConfig
            )

        use_async = llm_alarm_analyzer_config.async_processing
        analyzeLocationAlarm = llm_alarm_analyzer_config.analyzeLocationAlarm
        resolveRawAlarm = self._should_resolve_raw_alarm(
            llm_alarm_analyzer_config.resolveRawAlarm,
            alarm.tenant_id,
            alarm.source_entity_id,
            len(new_tags.entities),
        )
        escalateRawAlarm = llm_alarm_analyzer_config.escalateRawAlarm
        resolveLocationAlarm = llm_alarm_analyzer_config.resolveLocationAlarm
        sendInlineVideo = llm_alarm_analyzer_config.sendInlineVideo
        sendInlineFrames = llm_alarm_analyzer_config.sendInlineFrames
        slowDownVideoFlag = llm_alarm_analyzer_config.slowDownVideoFlag
        temperature = llm_alarm_analyzer_config.temperature
        update_location_alarm_tap = (
            llm_alarm_analyzer_config.updateLocationAlarmTap
        )
        usePreviousLocationAlarmExplanation = (
            llm_alarm_analyzer_config.usePreviousLocationAlarmExplanation
        )
        if self.side_effect_order == "pre":
            if resolveRawAlarm:
                use_async = False
                analyzeRawAlarm = True
                analyzeLocationAlarm = False
                resolveLocationAlarm = False
                update_location_alarm_tap = False
            else:
                log.info("Use pre side effect only when resolving Raw Alarms")
                return
        elif self.side_effect_order == "post":
            # use_async is same as config since async can be used here
            if resolveRawAlarm:
                # Raw alarm has been processed when "pre" side effect was called
                analyzeRawAlarm = False
                # analyzeLocationAlarm depends on config
                # resolveLocationAlarm depends on config
            else:
                # Raw alarm needs to be processed now
                analyzeRawAlarm = True
                # analyzeLocationAlarm depends on config
                # resolveLocationAlarm depends on config
        else:
            log.error("Invalid side effect order, skipping LLM alarm analyzer")
            return

        previous_analysis, previous_alarms, loc_alarm = self._setup_alarm_data(
            alarm, analyzeLocationAlarm, usePreviousLocationAlarmExplanation
        )
        if (
            loc_alarm is not None
            and loc_alarm.current_status == LocationAlarmStatus.RESOLVED
        ):
            log.info(
                "Location Alarm Status",
                loc_status=loc_alarm.current_status,
            )
            return

        if use_async and alarm.alarm_type == ALARM_TYPE_MOTION:
            task = (
                alarm,
                new_tags,
                loc_alarm,
                resolveRawAlarm,
                escalateRawAlarm,
                resolveLocationAlarm,
                update_location_alarm_tap,
                (
                    alarm.video_start_time_utc,
                    alarm.video_end_time_utc,
                ),
                sop_text,
                source_camera_name,
                location_name,
                location_timezone,
                previous_analysis,
                previous_alarms,
                analyzeRawAlarm,
                analyzeLocationAlarm,
                sendInlineVideo,
                sendInlineFrames,
                checkThreats,
                slowDownVideoFlag,
                temperature,
            )
            try:
                self.task_queue.put(task, block=False)
            except Exception:
                log.warning(
                    "Too many gemini calls in queue, considering increasing max queue size"
                )
        else:
            self.process_alarm(
                alarm,
                new_tags,
                loc_alarm,
                resolveRawAlarm,
                escalateRawAlarm,
                resolveLocationAlarm,
                update_location_alarm_tap,
                (
                    alarm.video_start_time_utc,
                    alarm.video_end_time_utc,
                ),
                sop_text,
                source_camera_name,
                location_name,
                location_timezone,
                previous_analysis,
                previous_alarms,
                analyzeRawAlarm,
                analyzeLocationAlarm,
                sendInlineVideo,
                sendInlineFrames,
                checkThreats,
                slowDownVideoFlag,
                temperature,
            )


@dataclass
class LLMAlarmAnalyzerSideEffectResults:
    ra_analysis: typing.Optional[str] = None
    ra_recommendation: typing.Optional[str] = None
    la_score: typing.Optional[int] = None
    la_recommendation: typing.Optional[str] = None
    la_summary: typing.Optional[str] = None
    la_explanation: typing.Optional[str] = None
    la_state: typing.Optional[typing.Dict] = None


def check_alarm(LLMAlarmAnalyzerSideEffectClass):
    alarm_id, tenant_id = sys.argv[1], sys.argv[2]

    db = RDSClient()
    cm = ctrl.ControllerMap(db.db_adapter)

    se = LLMAlarmAnalyzerSideEffectClass(cm, "post")
    alarm = db.get_alarm_object(alarm_id, tenant_id)
    assert alarm is not None
    processing_config = cm.alarm.get_processing_config(alarm)
    se.handle_side_effect(
        processing_config,
        alarm,
        50,
        Tags([], {"tags": ["ENTRY", "UNAUTHORIZED_ENTRY"]}),
        None,
        None,
        AlarmState.PROCESSED,
    )
