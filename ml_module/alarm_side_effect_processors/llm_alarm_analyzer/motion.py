import json
import tempfile
import time
import typing
from collections import defaultdict as dd
from datetime import datetime, timedelta
from pathlib import Path

import structlog

import controller as ctrl
from common_utils.db_pool import ctrl_pool
from common_utils.ffmpeg_helpers import slow_down_video
from common_utils.time_utils import (
    format_utc_to_local_with_weekday,
)
from common_utils.typing_helpers import NUMBER
from common_utils.video_utils import extract_and_encode_frames
from config import backend_config as config
from interfaces.alarm import ALARM_TYPE_MOTION, Alarm
from interfaces.location_alarms import (
    LocationAlarmStatus,
    LocationAlarmUpdateType,
)
from interfaces.tags import Tags
from interfaces.tenant_config import (
    AlarmProcessingConfig,
)
from ml_module.alarm_side_effect_processors.llm_alarm_analyzer.base import (
    LLMAlarmAnalyzerSideEffect,
    LLMAlarmAnalyzerSideEffectResults,
    check_alarm,
)
from ml_module.llm_alarm_analyzer.analyzer import (
    AIResultLA,
    AIResultRA,
)
from ml_module.utils import download_alarm_video
from models_rds.location_alarms.location_alarm_update import (
    LocationAlarmUpdates,
)
from models_rds.location_alarms.location_alarms import LocationAlarms
from models_rds.raw_alarms import RawAlarms

log = structlog.get_logger("hakimo", module="Motion LLM Alarm Analyzer")


class MotionLLMAlarmAnalyzerSideEffect(LLMAlarmAnalyzerSideEffect):
    def _should_process(
        self,
        alarm: Alarm,
        new_tap: NUMBER,
        processing_config: typing.Optional[AlarmProcessingConfig],
        new_tags: Tags,
    ) -> bool:
        if not alarm.display:
            log.info(
                "Alarm is hidden, skipping LLM alarm analyzer side effect"
            )
            return False
        if new_tap < 50:
            return False
        if not alarm.source_entity_id:
            log.warning("Alarm does not have a source entity id")
            return False
        # Disable only if config exists to explicitly disable, default is enabled
        if (
            processing_config is not None
            and processing_config.llmAlarmAnalyzerConfig is not None
            and not processing_config.llmAlarmAnalyzerConfig.enabled
        ):
            log.warning("LLM Alarm Analyzer is disabled, skipping side effect")
            return False
        if alarm.video_path is None:
            log.warning(
                "Alarm does not have video_path set, skipping LLM alarm analyzer"
            )
            return False
        if not alarm.alarm_type == ALARM_TYPE_MOTION:
            return False
        return True

    def _use_default_llm_alarm_analyzer_config(self) -> bool:
        return True

    def _should_resolve_raw_alarm(
        self,
        resolveRawAlarm: bool,
        tenant_id: str,
        source_entity_id: str,
        n_entities: int,
    ) -> bool:
        return resolveRawAlarm

    def _setup(self, alarm: Alarm):
        assert alarm.source_entity_id is not None
        source_camera = self._controller.camera.get_camera_by_id(
            alarm.source_entity_id
        )
        if not source_camera:
            log.error("Camera not found", camera_id=alarm.source_entity_id)
            return

        source_location_id = source_camera.location_id
        source_location = self._controller.locations.get_location_by_id(
            source_location_id
        )
        if not source_location:
            log.error("Location not found", location_id=source_location_id)
            return
        location_name = source_location.name
        location_timezone = source_location.timezone
        sop = self._controller.sop.get_sop(
            tenant_id=alarm.tenant_id, location_id=source_location_id
        )
        if not sop:
            log.error(
                "SOP not found",
                tenant_id=alarm.tenant_id,
                location_id=source_location_id,
            )
            return
        sop_text = self.transform_relevant_sections_of_sop(sop.sop)
        log.debug(
            "Transformed SOP",
            sop_text=sop_text,
            tenant_id=alarm.tenant_id,
            location_id=source_location_id,
        )
        source_camera_name = source_camera.name
        checkThreats = source_location.config.get("checkThreats", False)
        return (
            sop_text,
            source_camera_name,
            checkThreats,
            location_timezone,
            location_name,
        )

    def transform_relevant_sections_of_sop(self, sop: str) -> str:
        """Converts a SOP json to text for the prompt by extracting relevant
        sections. Each relevant section in the SOP dict must be a list of strings
        """
        try:
            sop_dict: typing.Dict[str, typing.Any] = json.loads(sop)

            if "sop_workflow" not in sop_dict or not isinstance(
                sop_dict["sop_workflow"], typing.Dict
            ):
                log.error(
                    "sop_workflow key not found in SOP", sop_dict=sop_dict
                )
                return sop
            sop_dict = sop_dict["sop_workflow"]

            # Relevant sections to be picked from the SOP with descriptions
            descriptions = {
                "emergencySituations": "Emergency Situations (important to escalate)",
                "nonEmergencySituations": "Non-Emergency Situations (still need to be escalated)",
                "exceptions": "Exceptions (situations describing expected behaviour)",
                "notes": "Notes (additional information)",
            }

            relevant_sop_sections: typing.Dict[str, typing.List] = dd(list)
            if "situations" in sop_dict:
                for situation in sop_dict["situations"]:
                    if (
                        isinstance(situation, dict)
                        and "label" in situation
                        and "color" in situation
                    ):
                        if situation["color"] == "red":
                            relevant_sop_sections[
                                "emergencySituations"
                            ].append(situation["label"])
                        elif situation["color"] == "green":
                            relevant_sop_sections[
                                "nonEmergencySituations"
                            ].append(situation["label"])
                        # Ignoring blue labels
            if "exceptions" in sop_dict and isinstance(
                sop_dict["exceptions"], list
            ):
                relevant_sop_sections["exceptions"] = sop_dict["exceptions"]
            if "notes" in sop_dict and isinstance(sop_dict["notes"], list):
                relevant_sop_sections["notes"] = sop_dict["notes"]

            sop_prompt = "\n".join(
                [
                    f"{descriptions.get(k) or k}: \n- " + "\n- ".join(v)
                    for k, v in relevant_sop_sections.items()
                ]
            )
            return sop_prompt
        except Exception as e:
            log.error("Error parsing SOP", error=e)
            return sop

    def _setup_alarm_data(
        self,
        alarm: Alarm,
        analyzeLocationAlarm: bool,
        usePreviousLocationAlarmExplanation: bool = False,
    ):
        previous_analysis: typing.List[typing.Dict] = []
        previous_alarms: typing.List[str] = []
        loc_alarm = None
        if analyzeLocationAlarm and alarm.alarm_type == ALARM_TYPE_MOTION:
            loc_alarms = self._controller.location_alarms.get_location_alarms_for_raw_alarm(
                alarm.alarm_uuid
            )
            if loc_alarms:
                # Populate raw alarms for location alarm
                loc_alarm = (
                    self._controller.location_alarms.get_location_alarm_by_id(
                        loc_alarms[0].int_id
                    )
                )
                assert loc_alarm is not None
                # This will contain analysis of current raw alarm if that was processed in "pre"
                previous_analysis, previous_alarms = (
                    self.get_previous_analysis(
                        loc_alarm, alarm, usePreviousLocationAlarmExplanation
                    )
                )
        return previous_analysis, previous_alarms, loc_alarm

    def process_alarm(
        self,
        alarm: Alarm,
        tags: Tags,
        loc_alarm: typing.Optional[LocationAlarms],
        resolve_raw_alarms: bool,
        escalate_raw_alarms: bool,
        resolve_loc_alarms: bool,
        update_location_alarm_tap: bool,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_name: str,
        location_timezone: str,
        previous_analysis: typing.List[typing.Dict[str, str]],
        previous_alarms: typing.List[str],
        analyzeRawAlarm: bool,
        analyzeLocationAlarm: bool,
        send_inline_video: bool,
        send_inline_frames: bool,
        check_threats: bool,
        slow_down_video_flag: bool,
        temperature: float,
    ):
        with tempfile.TemporaryDirectory() as tmp_dir:
            alarm_video_path = download_alarm_video(alarm.video_path, tmp_dir)
            start_ts, end_ts = video_times
            if start_ts is None:
                start_ts = alarm.alarm_time or alarm.created_at_utc
            if end_ts is None:
                end_ts = start_ts + timedelta(seconds=5)
            video_times = (start_ts, end_ts)
            log.info("Analyizng Alarm with Gemini", alarm_id=alarm.alarm_uuid)
            if slow_down_video_flag:
                slow_alarm_video_path = str(
                    Path(alarm_video_path).with_name(
                        Path(alarm_video_path).stem
                        + "_slow"
                        + Path(alarm_video_path).suffix
                    )
                )
                slow_down_video(alarm_video_path, slow_alarm_video_path)
                alarm_video_path = slow_alarm_video_path
            results: LLMAlarmAnalyzerSideEffectResults = (
                LLMAlarmAnalyzerSideEffectResults()
            )
            if check_threats:
                should_resolve_threat, threat_description = (
                    self._llm_threat_classify(
                        alarm.tenant_id, alarm_video_path, tags, False
                    )
                )
                if not should_resolve_threat:
                    self._publish_threat_alarm(alarm, threat_description)
                log.info(
                    "Threat classification done",
                    should_resolve=should_resolve_threat,
                    description=threat_description,
                )

            if analyzeRawAlarm:
                start_time = time.time()
                frames = None
                if send_inline_frames:
                    frames = extract_and_encode_frames(
                        alarm_video_path, video_times, fps=1
                    )
                    log.debug(
                        "Number of frames extracted", num_frames=len(frames)
                    )
                ai_result_ra: AIResultRA = self._analyzer.analyze_alarm(
                    tenant_id=alarm.tenant_id,
                    video_path=alarm_video_path,
                    video_times=video_times,
                    sop=sop,
                    camera_name=camera_name,
                    location_timezone=location_timezone,
                    send_inline_video=send_inline_video,
                    send_inline_frames=send_inline_frames,
                    temperature=temperature,
                    frames=frames,
                )
                time_taken = time.time() - start_time
                try:
                    input_type = "video"
                    if send_inline_video:
                        input_type = "video"
                    elif send_inline_frames:
                        input_type = "frames"
                    self._metrics["rg_llm_raw_alarms_processed"].labels(
                        tenant=alarm.tenant_id,
                        success=str(ai_result_ra.success),
                        reason=ai_result_ra.reason or "none",
                        recommendation=ai_result_ra.recommendation or "none",
                    ).inc()
                    self._metrics[
                        "rg_llm_raw_alarm_processing_seconds"
                    ].labels(
                        tenant=alarm.tenant_id,
                        camera=camera_name,
                        input_type=input_type,
                    ).observe(time_taken)
                    if ai_result_ra.input_tokens:
                        self._metrics["rg_llm_raw_alarm_tokens"].labels(
                            tenant=alarm.tenant_id,
                            camera=camera_name,
                            type="input",
                        ).inc(ai_result_ra.input_tokens)
                    if ai_result_ra.output_tokens:
                        self._metrics["rg_llm_raw_alarm_tokens"].labels(
                            tenant=alarm.tenant_id,
                            camera=camera_name,
                            type="output",
                        ).inc(ai_result_ra.output_tokens)

                except Exception as e:
                    log.warning(
                        "Failed to update RG metrics for raw alarm",
                        alarm_id=alarm.alarm_uuid,
                        error=str(e),
                    )

                results.ra_analysis = ai_result_ra.analysis
                results.ra_recommendation = ai_result_ra.recommendation
                if ai_result_ra.analysis and ai_result_ra.recommendation:
                    vs_str = format_utc_to_local_with_weekday(
                        video_times[0], location_timezone
                    )
                    ve_str = format_utc_to_local_with_weekday(
                        video_times[1], location_timezone
                    )
                    previous_alarms.append(alarm.alarm_uuid)
                    previous_analysis.append(
                        {
                            "text": (
                                f"Camera: {camera_name} "
                                f"Video start time: {vs_str} "
                                f"Video end time: {ve_str} "
                                f"Video explanation: {ai_result_ra.analysis} "
                            )
                        }
                    )
            if analyzeLocationAlarm and previous_analysis and loc_alarm:
                start_time = time.time()
                ai_result_la: AIResultLA = (
                    self._analyzer.analyze_location_alarm(
                        tenant_id=alarm.tenant_id,
                        previous_analysis=previous_analysis,
                        sop=sop,
                        location_timezone=location_timezone,
                        temperature=temperature,
                    )
                )
                time_taken = time.time() - start_time
                results.la_explanation = ai_result_la.explanation
                results.la_summary = ai_result_la.summary
                results.la_recommendation = ai_result_la.recommendation
                results.la_score = ai_result_la.score
                results.la_state = {"raw_alarms_id": previous_alarms}
                try:
                    self._metrics["rg_llm_location_alarms_processed"].labels(
                        tenant=alarm.tenant_id,
                        success=str(ai_result_la.success),
                        reason=ai_result_la.reason or "none",
                        recommendation=ai_result_la.recommendation or "none",
                    ).inc()
                    self._metrics[
                        "rg_llm_location_alarm_processing_seconds"
                    ].labels(
                        tenant=alarm.tenant_id,
                        location=location_name,
                    ).observe(time_taken)
                    if ai_result_la.input_tokens:
                        self._metrics["rg_llm_location_alarm_tokens"].labels(
                            tenant=alarm.tenant_id,
                            type="input",
                            location=location_name,
                        ).inc(ai_result_la.input_tokens)
                    if ai_result_la.output_tokens:
                        self._metrics["rg_llm_location_alarm_tokens"].labels(
                            tenant=alarm.tenant_id,
                            type="output",
                            location=location_name,
                        ).inc(ai_result_la.output_tokens)
                except Exception as e:
                    log.warning(
                        "Failed to update metrics for location alarm",
                        location_alarm_id=loc_alarm.int_id,
                        error=str(e),
                    )
            log.info("Finish gemini request", alarm_id=alarm.alarm_uuid)
            self.postprocess(
                alarm,
                loc_alarm,
                resolve_raw_alarms,
                escalate_raw_alarms,
                resolve_loc_alarms,
                update_location_alarm_tap,
                results,
            )

    def postprocess(
        self,
        alarm: Alarm,
        loc_alarm: typing.Optional[LocationAlarms],
        resolve_raw_alarms: bool,
        escalate_raw_alarms: bool,
        resolve_loc_alarms: bool,
        update_location_alarm_tap: bool,
        result: LLMAlarmAnalyzerSideEffectResults,
    ):
        with ctrl_pool.get() as thread_safe_controller:  # type: ignore
            controller: ctrl.ControllerMap = thread_safe_controller
            log.info("Alarm Analyzer Postprocess", result=result)
            if result.ra_analysis and result.ra_recommendation:
                controller.ai_outputs.add_ai_output_for_raw_alarm(
                    raw_alarm_id=alarm.alarm_uuid,
                    tenant_id=alarm.tenant_id,
                    analysis=result.ra_analysis,
                    recommendation=result.ra_recommendation,
                )
                log.info("Added to AI output table")
                controller.alarm.add_comment(
                    alarm_id=alarm.alarm_uuid,
                    tenant_id=alarm.tenant_id,
                    comment_str=f"Recommendation:{result.ra_recommendation}, Analysis: {result.ra_analysis}",
                )
                log.info("Added comment")
                if (
                    resolve_raw_alarms
                    and result.ra_recommendation.lower() == "resolve"
                ):
                    log.info("Resolving raw alarm")
                    controller.alarm.update_tap(
                        alarm_id=alarm.alarm_uuid,
                        tenant_id=alarm.tenant_id,
                        tap=39,
                        new_ml_outputs=False,
                    )
                    controller.alarm.resolve_alarm(
                        alarm_id=alarm.alarm_uuid, tenant_id=alarm.tenant_id
                    )
                if (
                    escalate_raw_alarms
                    and result.ra_recommendation.lower() == "escalate"
                ):
                    log.info("Escalating raw alarm")
                    # Increase TAP to 95
                    controller.alarm.update_tap(
                        alarm_id=alarm.alarm_uuid,
                        tenant_id=alarm.tenant_id,
                        tap=95,
                        new_ml_outputs=False,
                    )

            if (
                result.la_summary
                and result.la_recommendation
                and result.la_state
                and result.la_explanation
                and loc_alarm
            ):
                controller.ai_outputs.add_ai_output_for_location_alarm(
                    location_alarm_id=loc_alarm.int_id,
                    tenant_id=alarm.tenant_id,
                    summary=result.la_summary,
                    recommendation=result.la_recommendation,
                    raw_state=result.la_state,
                    explanation=result.la_explanation,
                )
                location_alarm_update = LocationAlarmUpdates(
                    location_alarm_id=loc_alarm.int_id,
                    update_type=LocationAlarmUpdateType.ADD_AI_COMMENT,
                    update_text=f"Recommendation:{result.la_recommendation}  Summary:{result.la_summary}",
                    user_id=config.HAIE.HAKIMO_USER_ID,
                )
                controller.location_alarms.update_location_alarm(
                    location_alarm_update=location_alarm_update,
                    location_alarm=loc_alarm,
                )
                if (
                    resolve_loc_alarms
                    and result.la_recommendation.lower() == "resolve"
                ):
                    controller.location_alarms.update_location_alarm_status(
                        location_alarm=loc_alarm,
                        new_status=LocationAlarmStatus.RESOLVED,
                        update_text="Resolved by AI Operator",
                        user_id=config.HAIE.HAKIMO_USER_ID,
                    )
                if (
                    update_location_alarm_tap
                    and result.la_score
                    and result.la_score >= 50
                    and loc_alarm.tap != result.la_score
                ):
                    controller.location_alarms.update_location_alarm_status(
                        location_alarm=loc_alarm,
                        tap=result.la_score,
                        update_text=f"Location Alarm TAP Updated by AI Operator to {result.la_score}",
                        user_id=config.HAIE.HAKIMO_USER_ID,
                    )

    def get_previous_analysis(
        self,
        loc_alarm: LocationAlarms,
        current_alarm: Alarm,
        usePreviousLocationAlarmExplanation: bool = False,
    ) -> typing.Tuple[typing.List[typing.Dict], typing.List[str]]:
        previous_analysis: typing.List[typing.Dict] = []
        previous_alarms: typing.List[str] = []
        if not isinstance(loc_alarm.raw_alarms, list):
            return previous_analysis, previous_alarms
        if usePreviousLocationAlarmExplanation:
            la_ai_output = self._controller.ai_outputs.get_latest_ai_output_for_location_alarm(
                loc_alarm.int_id
            )
            if la_ai_output is not None:
                raw_state = la_ai_output.raw_state
                latest_ai_explanation = (
                    la_ai_output.explanation
                    if la_ai_output.explanation
                    else la_ai_output.summary
                )
                if raw_state and latest_ai_explanation:
                    # latest explanation of location alarm
                    previous_analysis.append(
                        {
                            "text": (
                                "Here is a summary of the recent activity at this site, which includes notable incidents and observations detected in earlier video events. "
                                f"summary: {latest_ai_explanation}\n"
                                "Here is the analysis from new video events from this site:"
                            )
                        }
                    )
                    for ra in loc_alarm.raw_alarms:
                        if ra.uuid in raw_state["raw_alarms_id"]:
                            previous_alarms.append(ra.uuid)
                            continue
                        ra_subprompt = self.compose_subprompt_from_raw_alarm(
                            ra
                        )
                        if ra_subprompt is None:
                            continue
                        previous_alarms.append(ra.uuid)
                        previous_analysis.append(ra_subprompt)
                    return previous_analysis, previous_alarms
        for ra in loc_alarm.raw_alarms:
            ra_subprompt = self.compose_subprompt_from_raw_alarm(ra)
            if ra_subprompt is None:
                continue
            previous_alarms.append(ra.uuid)
            previous_analysis.append(ra_subprompt)
        return previous_analysis, previous_alarms

    def compose_subprompt_from_raw_alarm(self, raw_alarm: Alarm):
        assert isinstance(raw_alarm, RawAlarms)
        assert raw_alarm.source_entity_id is not None
        ra_cam = self._controller.camera.get_camera_by_id(
            raw_alarm.source_entity_id, include_location=True
        )
        assert ra_cam is not None
        ra_cam_name = ra_cam.name
        ra_ai_output = (
            self._controller.ai_outputs.get_latest_ai_output_for_raw_alarm(
                raw_alarm.uuid
            )
        )
        if ra_ai_output is None:
            return None

        ra_ai_analysis = ra_ai_output.analysis
        tz_name = ra_cam.location.timezone
        ra_vs_str = format_utc_to_local_with_weekday(
            raw_alarm.video_start_timestamp_utc, tz_name
        )
        ra_ve_str = format_utc_to_local_with_weekday(
            raw_alarm.video_end_timestamp_utc, tz_name
        )

        ra_subprompt = {
            "text": (
                f"Camera: {ra_cam_name} "
                f"Video start time: {ra_vs_str} "
                f"Video end time: {ra_ve_str} "
                f"Video explanation: {ra_ai_analysis} "
            )
        }
        return ra_subprompt


if __name__ == "__main__":
    check_alarm(MotionLLMAlarmAnalyzerSideEffect)
