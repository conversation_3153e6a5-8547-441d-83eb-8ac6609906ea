import tempfile
import typing
from datetime import datetime

import structlog

import controller as ctrl
from common_utils.cloud_config_utils import cloud_config_manager
from common_utils.db_pool import ctrl_pool
from common_utils.typing_helpers import NUMBER
from interfaces.alarm import ALARM_TYPE_MOTION, Alarm
from interfaces.tags import Tags, TagTypes
from interfaces.tenant_config import (
    AlarmProcessingConfig,
)
from ml_module.alarm_side_effect_processors.llm_alarm_analyzer.base import (
    LLMAlarmAnalyzerSideEffect,
    check_alarm,
)
from ml_module.alarm_side_effect_processors.llm_alarm_analyzer.utils import (
    AlarmEventHandler,
)
from ml_module.llm_alarm_analyzer.analyzer import (
    LLMRequestResponse,
)
from ml_module.utils import download_alarm_video
from models_rds.location_alarms.location_alarms import LocationAlarms

log = structlog.get_logger("hakimo", module="Enterprise LLM Alarm Analyzer")


class EnterpriseLLMAlarmAnalyzerSideEffect(LLMAlarmAnalyzerSideEffect):
    def __init__(
        self,
        controller: ctrl.ControllerMap,
        side_effect_order: typing.Optional[str] = None,
    ):
        side_effect_order = "post"
        super().__init__(controller, side_effect_order)
        self._event_handler = AlarmEventHandler(controller, self._rds_client)

    def _should_process(
        self,
        alarm: Alarm,
        new_tap: NUMBER,
        processing_config: typing.Optional[AlarmProcessingConfig],
        new_tags: Tags,
    ) -> bool:
        if not alarm.display:
            log.info(
                "Alarm is hidden, skipping LLM alarm analyzer side effect"
            )
            return False
        if new_tap < 50:
            return False
        if not alarm.source_entity_id:
            log.warning("Alarm does not have a source entity id")
            return False
        # Enable only if config exists to explicitly enable, default is disabled
        if (
            processing_config is None
            or not processing_config.llmAlarmAnalyzerConfig
            or not processing_config.llmAlarmAnalyzerConfig.enabled
        ):
            log.warning("LLM Alarm Analyzer is disabled, skipping side effect")
            return False
        if alarm.video_path is None:
            log.warning(
                "Alarm does not have video_path set, skipping LLM alarm analyzer"
            )
            return False
        if alarm.alarm_type == ALARM_TYPE_MOTION:
            return False
        elif (
            TagTypes.UNAUTHORIZED_ENTRY.name not in new_tags.video_tag_names
        ) and (TagTypes.ENTRY.name not in new_tags.video_tag_names):
            return False
        return True

    def _use_default_llm_alarm_analyzer_config(self) -> bool:
        return False

    def _should_resolve_raw_alarm(
        self,
        resolveRawAlarm: bool,
        tenant_id: str,
        source_entity_id: str,
        n_entities: int,
    ) -> bool:
        max_entities = cloud_config_manager.is_enabled(
            "tailgating_llm_autoresolve.max_entities"
        )
        if isinstance(max_entities, int):
            if max_entities < n_entities:
                return False
        enabled_doors = cloud_config_manager.is_enabled(
            "tailgating_llm_autoresolve.enabled_doors"
        )
        if isinstance(enabled_doors, list):
            if source_entity_id in enabled_doors:
                return True
        enabled_tenants = cloud_config_manager.is_enabled(
            "tailgating_llm_autoresolve.enabled_tenants"
        )
        if isinstance(enabled_tenants, list):
            if tenant_id in enabled_tenants:
                return True
        if resolveRawAlarm:
            return True
        return False

    def _llm_enterprise_classify(
        self, alarm: Alarm, alarm_video_path: str, tags: Tags
    ) -> typing.Tuple[bool, str]:
        ag_times = self._event_handler.get_nearby_alarm_times(alarm)
        event_times = AlarmEventHandler.get_event_times(
            alarm, alarm_video_path, tags
        )
        only_event_times = [x[0] for x in event_times]
        is_split, split_times = AlarmEventHandler.get_split_times(
            ag_times, only_event_times, alarm
        )
        responses = []
        for st_split, en_split in split_times:
            if is_split:
                alarm_video_path = AlarmEventHandler.split_video_into_segments(
                    alarm_video_path,
                    (st_split - alarm.video_start_time_utc).total_seconds(),
                    (en_split - alarm.video_start_time_utc).total_seconds(),
                )
            annotated_alarm_video_path = AlarmEventHandler.annotate_video(
                alarm, alarm_video_path
            )
            text = AlarmEventHandler.construct_enterprise_text(
                [t for t in ag_times if (t < en_split and t > st_split)],
                st_split,
                [
                    t
                    for t in only_event_times
                    if (t < en_split and t > st_split)
                ],
            )
            llm_response: LLMRequestResponse = (
                self._analyzer._client.send_request_with_video(
                    tenant_id=alarm.tenant_id,
                    text=text,
                    video_path=annotated_alarm_video_path,
                    use_video_uri=False,
                )
            )
            if llm_response.success:
                responses.append(llm_response.data)
        should_resolve = True
        descriptions = []
        for resp in responses:
            if "escalate" in resp["recommendation"].lower():
                should_resolve = False
            descriptions.append(resp["description"])
        return should_resolve, ". ".join(descriptions)

    def _setup_alarm_data(
        self,
        alarm: Alarm,
        analyzeLocationAlarm: bool,
        usePreviousLocationAlarmExplanation: bool = False,
    ):
        return [], [], None

    def _setup(self, alarm: Alarm):
        source_door = self._controller.door.get_door(alarm.source_entity_id)
        assert source_door is not None
        source_location_id = source_door.location_id
        source_location = self._controller.locations.get_location_by_id(
            source_location_id
        )
        if not source_location:
            log.error("Location not found", location_id=source_location_id)
            return
        location_timezone = source_location.timezone
        checkThreats = source_location.config.get("checkThreats", False)
        sop_text = ""
        source_camera_name = ""
        location_name = source_location.name
        return (
            sop_text,
            source_camera_name,
            checkThreats,
            location_timezone,
            location_name,
        )

    def process_alarm(
        self,
        alarm: Alarm,
        tags: Tags,
        loc_alarm: typing.Optional[LocationAlarms],
        resolve_raw_alarms: bool,
        escalate_raw_alarms: bool,
        resolve_loc_alarms: bool,
        update_location_alarm_tap: bool,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_name: str,
        location_timezone: str,
        previous_analysis: typing.List[typing.Dict[str, str]],
        previous_alarms: typing.List[str],
        analyzeRawAlarm: bool,
        analyzeLocationAlarm: bool,
        send_inline_video: bool,
        send_inline_frames: bool,
        check_threats: bool,
        slow_down_video_flag: bool,
        temperature: float,
    ):
        threat_description, classify_description = None, None
        should_resolve, should_resolve_classify = False, False

        with tempfile.TemporaryDirectory() as tmp_dir:
            alarm_video_path = download_alarm_video(alarm.video_path, tmp_dir)
            if check_threats:
                should_resolve_threat, threat_description = (
                    self._llm_threat_classify(
                        alarm.tenant_id, alarm_video_path, tags, True
                    )
                )
                should_resolve = should_resolve or should_resolve_threat
                if not should_resolve_threat:
                    self._publish_threat_alarm(alarm, threat_description)
                log.info(
                    "Threat classification done",
                    should_resolve=should_resolve_threat,
                    description=threat_description,
                )
            if TagTypes.UNAUTHORIZED_ENTRY.name in tags.video_tag_names:
                should_resolve_classify, classify_description = (
                    self._llm_enterprise_classify(
                        alarm, alarm_video_path, tags
                    )
                )
                should_resolve = should_resolve or should_resolve_classify
                log.info(
                    "Unauthorized entry classification done",
                    should_resolve=should_resolve_classify,
                    description=classify_description,
                )
        if classify_description is not None:
            with ctrl_pool.get() as controller:  # type: ignore
                if should_resolve_classify and resolve_raw_alarms:
                    assert alarm.source_id is not None
                    tailgating_source_id = "derived/" + alarm.source_id
                    tailgating_uuid = controller.alarm.alarm_id_by_source_id(
                        tailgating_source_id, alarm.tenant_id
                    )
                    controller.alarm.resolve_alarm(
                        alarm_id=tailgating_uuid, tenant_id=alarm.tenant_id
                    )
                    controller.alarm.update_tap(
                        tailgating_uuid,
                        alarm.tenant_id,
                        37,
                    )
                controller.ai_outputs.add_ai_output_for_raw_alarm(
                    raw_alarm_id=alarm.alarm_uuid,
                    tenant_id=alarm.tenant_id,
                    analysis=classify_description,
                    recommendation="Resolve" if should_resolve else "Escalate",
                )
                controller.alarm.add_comment(
                    alarm_id=alarm.alarm_uuid,
                    tenant_id=alarm.tenant_id,
                    comment_str=classify_description,
                )


if __name__ == "__main__":
    check_alarm(EnterpriseLLMAlarmAnalyzerSideEffect)
